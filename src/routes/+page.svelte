<script lang="ts">
	import { TextInput } from '$lib/components/text-input/index.js';
	import { Button } from '$lib/components/button/index.js';
	import { TextInputType } from '$lib/components/text-input/text-input.enums.js';
	import { ComponentSize } from '$lib/enums/index.js';
	import { User, Lock, Envelope, CircleNotch, Spinner, Circle } from 'phosphor-svelte';

	// Form state
	let email = $state('<EMAIL>');
	let password = $state('password123');
	let username = $state('johndoe');

	// Message states - cycling through: error -> success -> none
	let emailState = $state<'error' | 'success' | 'none'>('none');
	let passwordState = $state<'error' | 'success' | 'none'>('none');
	let usernameState = $state<'error' | 'success' | 'none'>('none');

	// Helper text and error state derivations
	const emailHelperText = $derived(() => {
		switch (emailState) {
			case 'error':
				return 'Please enter a valid email address';
			case 'success':
				return 'Email address looks good!';
			case 'none':
				return '';
		}
	});

	const passwordHelperText = $derived(() => {
		switch (passwordState) {
			case 'error':
				return 'Password must be at least 8 characters';
			case 'success':
				return 'Strong password!';
			case 'none':
				return '';
		}
	});

	const usernameHelperText = $derived(() => {
		switch (usernameState) {
			case 'error':
				return 'Username is already taken';
			case 'success':
				return 'Username is available';
			case 'none':
				return '';
		}
	});

	// Toggle functions
	const stateOrder: Array<'none' | 'error' | 'success'> = ['none', 'error', 'success'];

	function getNextState(currentState: 'none' | 'error' | 'success'): 'none' | 'error' | 'success' {
		const currentIndex = stateOrder.indexOf(currentState);
		return stateOrder[(currentIndex + 1) % stateOrder.length];
	}

	function toggleEmailState() {
		emailState = getNextState(emailState);
	}

	function togglePasswordState() {
		passwordState = getNextState(passwordState);
	}

	function toggleUsernameState() {
		usernameState = getNextState(usernameState);
	}

	function toggleAllStates() {
		toggleEmailState();
		togglePasswordState();
		toggleUsernameState();
	}
</script>

<div class="bg-background min-h-screen p-8">
	<div class="mx-auto max-w-md space-y-6">
		<div class="text-center">
			<h1 class="text-foreground text-2xl font-bold">Text Input Testing Form</h1>
			<p class="text-muted-foreground mt-2">Test error, success, and neutral states</p>
		</div>

		<form class="space-y-4">
			<!-- Email Input -->
			<TextInput
				bind:value={email}
				type={TextInputType.EMAIL}
				size={ComponentSize.MD}
				label="Email Address"
				placeholder="Enter your email"
				name="email"
				helperText={emailHelperText()}
				error={emailState === 'error'}
				showClearButton={true}
				required
			>
				{#snippet adornment()}
					<Envelope size={20} />
				{/snippet}
			</TextInput>

			<!-- Username Input -->
			<TextInput
				bind:value={username}
				type={TextInputType.TEXT}
				size={ComponentSize.MD}
				label="Username"
				placeholder="Choose a username"
				name="username"
				helperText={usernameHelperText()}
				error={usernameState === 'error'}
				showClearButton={true}
				required
			>
				{#snippet adornment()}
					<User size={20} />
				{/snippet}
			</TextInput>

			<!-- Password Input -->
			<TextInput
				bind:value={password}
				type={TextInputType.PASSWORD}
				size={ComponentSize.MD}
				label="Password"
				placeholder="Enter your password"
				name="password"
				helperText={passwordHelperText()}
				error={passwordState === 'error'}
				showClearButton={true}
				required
			>
				{#snippet adornment()}
					<Lock size={20} />
				{/snippet}
			</TextInput>
		</form>

		<!-- Control Buttons -->
		<div class="space-y-3 pt-4">
			<div class="grid grid-cols-3 gap-2">
				<Button onclick={toggleEmailState} size={ComponentSize.SM}>
					Email: {emailState}
				</Button>
				<Button onclick={togglePasswordState} size={ComponentSize.SM}>
					Password: {passwordState}
				</Button>
				<Button onclick={toggleUsernameState} size={ComponentSize.SM}>
					Username: {usernameState}
				</Button>
			</div>

			<Button onclick={toggleAllStates} size={ComponentSize.MD} class="w-full">
				Toggle All States
			</Button>
		</div>

		<!-- Current State Display -->
		<div class="bg-muted rounded-md p-4 text-sm">
			<h3 class="text-foreground mb-2 font-medium">Current States:</h3>
			<ul class="text-muted-foreground space-y-1">
				<li>Email: <span class="font-mono">{emailState}</span></li>
				<li>Password: <span class="font-mono">{passwordState}</span></li>
				<li>Username: <span class="font-mono">{usernameState}</span></li>
			</ul>
		</div>
	</div>
</div>
<CircleNotch class="spinner" size={32} weight="bold" />

<style>
	:global(.spinner) {
		animation: spin 1s linear infinite;
		display: inline-block;
	}

	@keyframes spin {
		from {
			transform: rotate(0deg);
		}
		to {
			transform: rotate(360deg);
		}
	}
</style>
