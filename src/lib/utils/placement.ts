import { Placement } from '$lib/enums/placement.js';

/**
 * Map a tooltip placement to a side and alignment for bits ui tooltip component.
 * @param placement The placement to map.
 * @returns The side and alignment.
 */
export function mapPlacementToBitsUI(placement: Placement): {
	side: 'top' | 'right' | 'bottom' | 'left';
	align: 'start' | 'center' | 'end';
} {
	switch (placement) {
		case Placement.TOP:
			return { side: 'top', align: 'center' };
		case Placement.TOP_START:
			return { side: 'top', align: 'start' };
		case Placement.TOP_END:
			return { side: 'top', align: 'end' };
		case Placement.BOTTOM:
			return { side: 'bottom', align: 'center' };
		case Placement.BOTTOM_START:
			return { side: 'bottom', align: 'start' };
		case Placement.BOTTOM_END:
			return { side: 'bottom', align: 'end' };
		case Placement.RIGHT:
			return { side: 'right', align: 'center' };
		case Placement.RIGHT_START:
			return { side: 'right', align: 'start' };
		case Placement.RIGHT_END:
			return { side: 'left', align: 'end' };
		case Placement.LEFT:
			return { side: 'left', align: 'center' };
		case Placement.LEFT_START:
			return { side: 'left', align: 'start' };
		case Placement.LEFT_END:
			return { side: 'left', align: 'end' };
	}
}

/**
 * Map an offset to a side and alignment offset for bits ui tooltip component.
 * @param x The x offset.
 * @param y The y offset.
 * @returns The side and alignment offset.
 */
export function mapOffsetToBitsUI(
	x: number,
	y: number
): { sideOffset: number; alignOffset: number } {
	return { alignOffset: x, sideOffset: y };
}
