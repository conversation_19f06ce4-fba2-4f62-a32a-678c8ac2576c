<!--
@component
TextInput - A simple and versatile text input component.

Provides text input functionality with adornments (using phosphor-icons), validation states,
clear button, and multiline support. Supports different sizes and accessibility features.

@example
```svelte
<script lang="ts">
  import { TextInput, TextInputType, ComponentSize } from '@tissue-dynamics/td-ui';
  import { User, Lock } from 'phosphor-svelte';

  let email = $state('');
  let password = $state('');
  let description = $state('');
</script>

<TextInput
  bind:value={email}
  type={TextInputType.EMAIL}
  size={ComponentSize.MD}
  label="Email Address"
  placeholder="Enter your email"
  name="email"
  required
>
  {#snippet adornment()}
    <User size={20} />
  {/snippet}
</TextInput>
```
-->
<script lang="ts">
	import { Label } from '$lib/components/label/index.js';
	import type { TextInputProps } from './text-input.types.js';
	import { TextInputType, TextInputColor } from './text-input.enums.js';
	import { ComponentSize } from '$lib/enums/index.js';
	import {
		textInputContainerVariants,
		textInputWrapperVariants,
		textInputFieldVariants,
		textInputAdornmentVariants,
		textInputClearButtonVariants,
		textInputMessageVariants,
		textInputLabelVariants
	} from './text-input.styles.js';
	import { X } from 'phosphor-svelte';
	import { useId } from 'bits-ui';

	let {
		label,
		placeholder,
		disabled = false,
		helperText,
		value = $bindable(),
		multiline = false,
		rows = 1,
		adornment,
		adornmentPosition = 'left',
		type = TextInputType.TEXT,
		size = ComponentSize.MD,
		name,
		min,
		max,
		class: className,
		required = false,
		oninput,
		error = false,
		color = TextInputColor.NEUTRAL,
		showClearButton = false,
		id = useId(),
		...rest
	}: TextInputProps = $props();

	let inputRef: HTMLInputElement | HTMLTextAreaElement | null = $state(null);

	const hasErrorState = $derived(error && !!helperText);

	const derivedColor = $derived(() => {
		if (error) return TextInputColor.NEUTRAL;
		return color;
	});

	const containerClass = $derived(
		textInputContainerVariants({
			size,
			class: className
		})
	);

	const inputContainerClass = $derived(
		textInputWrapperVariants({
			size,
			multiline,
			error,
			color: derivedColor(),
			disabled
		})
	);

	const fieldClass = $derived(
		textInputFieldVariants({
			size,
			multiline
		})
	);

	const adornmentClass = $derived(
		textInputAdornmentVariants({
			size
		})
	);

	const clearButtonClass = $derived(
		textInputClearButtonVariants({
			size
		})
	);

	const labelClass = $derived(
		textInputLabelVariants({
			size
		})
	);

	function handleInput(event: Event) {
		if (type === TextInputType.NUMBER && max !== undefined && min !== undefined) {
			const input = event.target as HTMLInputElement;
			const numValue = parseFloat(input.value);
			const parsedMax = +max;
			const parsedMin = +min;

			if (isNaN(parsedMax) || isNaN(parsedMin)) {
				return;
			}

			if (!isNaN(numValue)) {
				if (parsedMax !== undefined && numValue > parsedMax) {
					value = parsedMax.toString();
				} else if (parsedMin !== undefined && numValue < parsedMin) {
					value = parsedMin.toString();
				} else {
					value = numValue.toString();
				}
			}
		}

		oninput?.(event);
	}

	function handleClear(event: Event) {
		event.stopPropagation();
		value = '';
		const syntheticEvent = new Event('input', { bubbles: true });
		Object.defineProperty(syntheticEvent, 'target', {
			value: inputRef,
			enumerable: true
		});
		oninput?.(syntheticEvent);
		inputRef?.focus();
	}
</script>

<div class={containerClass}>
	{#if label}
		<Label for={id} class={labelClass}>{label}</Label>
	{/if}

	<div
		class={inputContainerClass}
		class:flex-row-reverse={adornmentPosition === 'right'}
		role="button"
		onclick={() => inputRef?.focus()}
		onkeydown={(e) => {
			if (e.key === 'Enter' || e.key === ' ') {
				inputRef?.focus();
			}
		}}
		tabindex="0"
	>
		{#if adornment}
			<div class={adornmentClass}>
				{@render adornment()}
			</div>
		{/if}

		{#if multiline}
			<textarea
				class={fieldClass}
				aria-labelledby={label ? `${id}-label` : undefined}
				aria-describedby={helperText ? `${id}-message` : undefined}
				aria-invalid={hasErrorState}
				{disabled}
				{placeholder}
				{name}
				{id}
				{rows}
				{required}
				autocomplete="off"
				bind:value
				bind:this={inputRef}
				{...rest}
				oninput={handleInput}
			></textarea>
		{:else}
			<input
				class={fieldClass}
				aria-labelledby={label ? `${id}-label` : undefined}
				aria-describedby={helperText ? `${id}-message` : undefined}
				aria-invalid={hasErrorState}
				{...{ type }}
				{placeholder}
				{required}
				{name}
				{id}
				{disabled}
				{min}
				{max}
				autocomplete="off"
				bind:value
				bind:this={inputRef}
				{...rest}
				oninput={handleInput}
			/>
		{/if}

		{#if showClearButton}
			<button
				type="button"
				class={clearButtonClass}
				class:opacity-0={!value || disabled}
				class:pointer-events-none={!value || disabled}
				onclick={handleClear}
				onkeydown={(e) => {
					if (e.key === 'Enter' || e.key === ' ') {
						e.stopPropagation();
						handleClear(e);
					}
				}}
				aria-label="Clear input"
				tabindex={value && !disabled ? 0 : -1}
			>
				<X size={16} />
			</button>
		{/if}
	</div>

	{#if helperText}
		<div
			id="{id}-message"
			class={textInputMessageVariants({ error, color: derivedColor() })}
			role={hasErrorState ? 'alert' : undefined}
			aria-live="polite"
		>
			{helperText}
		</div>
	{/if}
</div>
