import type { ComponentSize } from '$lib/enums/index.js';
import type { TextInputType, TextInputColor } from './text-input.enums.js';
import type { Snippet } from 'svelte';

export type TextInputConfig = {
	size?: ComponentSize;
	class?: string;
};

export type TextInputProps = {
	id?: string;
	label?: string;
	placeholder?: string;
	disabled?: boolean;
	helperText?: string;
	value?: string | number;
	multiline?: boolean;
	rows?: number;
	adornment?: Snippet;
	adornmentPosition?: 'left' | 'right';
	type?: TextInputType;
	size?: ComponentSize;
	name: string;
	class?: string;
	min?: number | string;
	max?: number | string;
	required?: boolean;
	oninput?: (event: Event) => void;
	error?: boolean;
	color?: TextInputColor;
	showClearButton?: boolean;
};
