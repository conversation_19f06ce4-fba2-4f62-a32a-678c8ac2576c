import type { TextInputConfig } from './text-input.types.js';
import { ComponentSize } from '$lib/enums/index.js';
import { TextInputColor } from './text-input.enums.js';

const baseContainerStyles = ['group', 'flex', 'flex-col', 'gap-1'].join(' ');

const baseInputContainerStyles = [
	'box-border',
	'flex',
	'cursor-text',
	'items-center',
	'justify-between',
	'gap-2',
	'rounded-md',
	'px-2',
	'py-1.5',
	'border',
	'border-border-input',
	'bg-background',
	'transition-all',
	'duration-200',
	'ease-in-out',
	'hover:border-border-input-hover',
	'focus-within:border-dark-40',
	'focus-within:outline-none',
	'focus-within:ring-0'
].join(' ');

const baseInputStyles = [
	'w-full',
	'bg-inherit',
	'placeholder:text-muted-foreground',
	'focus:outline-none',
	'focus:ring-0',
	'focus:border-transparent',
	'disabled:cursor-not-allowed',
	// Hide browser's native clear buttons
	'[&::-webkit-search-cancel-button]:appearance-none',
	'[&::-webkit-search-decoration]:appearance-none',
	'[&::-ms-clear]:hidden',
	'[&::-ms-reveal]:hidden'
].join(' ');

const baseTextareaStyles = [
	'w-full',
	'resize-none',
	'bg-inherit',
	'placeholder:text-muted-foreground',
	'focus:outline-none',
	'focus:ring-0',
	'focus:border-transparent',
	'disabled:cursor-not-allowed'
].join(' ');

const baseClearButtonStyles = [
	'aspect-square',
	'cursor-pointer',
	'rounded-md',
	'flex',
	'items-center',
	'justify-center',
	'transition-all',
	'duration-200',
	'ease-in-out',
	'hover:bg-destructive',
	'hover:text-white',
	'text-muted-foreground'
].join(' ');

const baseAdornmentStyles = [
	'flex',
	'items-center',
	'justify-center',
	'transition-colors',
	'duration-200',
	'ease-in-out',
	'text-muted-foreground',
	'group-focus-within:text-foreground'
].join(' ');

const baseMessageStyles = 'text-xs';

const baseLabelStyles = ['font-medium', 'leading-none', 'text-foreground'].join(' ');

const disabledStyles = [
	'cursor-not-allowed',
	'opacity-50',
	'[&_*]:cursor-not-allowed',
	'hover:border-border-input'
].join(' ');

const sizeStyles = {
	[ComponentSize.SM]: {
		container: 'h-7',
		input: 'text-xs',
		adornment: 'size-4',
		clearButton: 'h-4',
		label: 'text-xs'
	},
	[ComponentSize.MD]: {
		container: 'h-9',
		input: 'text-sm',
		adornment: 'size-5',
		clearButton: 'h-5',
		label: 'text-sm'
	},
	[ComponentSize.LG]: {
		container: 'h-10',
		input: 'text-base',
		adornment: 'size-6',
		clearButton: 'h-6',
		label: 'text-base'
	}
};

const borderColorStyles = {
	[TextInputColor.NEUTRAL]: '',
	[TextInputColor.SUCCESS]: 'border-green-500 hover:border-green-600',
	[TextInputColor.WARNING]: 'border-yellow-500 hover:border-yellow-600',
	[TextInputColor.INFO]: 'border-blue-500 hover:border-blue-600'
};

const errorBorderStyles = 'border-destructive hover:border-destructive';

export function textInputContainerVariants(config: TextInputConfig = {}): string {
	const { class: additionalClasses = '' } = config;
	return [baseContainerStyles, additionalClasses].filter(Boolean).join(' ');
}

export function textInputWrapperVariants(
	config: TextInputConfig & {
		multiline?: boolean;
		error?: boolean;
		color?: TextInputColor;
		disabled?: boolean;
	} = {}
): string {
	const {
		size = ComponentSize.MD,
		multiline = false,
		error = false,
		color = TextInputColor.NEUTRAL,
		disabled = false,
		class: additionalClasses = ''
	} = config;

	const borderStyles = error ? errorBorderStyles : borderColorStyles[color];

	return [
		baseInputContainerStyles,
		sizeStyles[size].container,
		multiline && 'h-auto',
		borderStyles,
		disabled && disabledStyles,
		additionalClasses
	]
		.filter(Boolean)
		.join(' ');
}

export function textInputFieldVariants(
	config: TextInputConfig & {
		multiline?: boolean;
	} = {}
): string {
	const { size = ComponentSize.MD, multiline = false, class: additionalClasses = '' } = config;

	return [
		multiline ? baseTextareaStyles : baseInputStyles,
		sizeStyles[size].input,
		additionalClasses
	]
		.filter(Boolean)
		.join(' ');
}

export function textInputAdornmentVariants(config: TextInputConfig = {}): string {
	const { size = ComponentSize.MD, class: additionalClasses = '' } = config;

	return [baseAdornmentStyles, sizeStyles[size].adornment, additionalClasses]
		.filter(Boolean)
		.join(' ');
}

export function textInputClearButtonVariants(config: TextInputConfig = {}): string {
	const { size = ComponentSize.MD, class: additionalClasses = '' } = config;

	return [baseClearButtonStyles, sizeStyles[size].clearButton, additionalClasses]
		.filter(Boolean)
		.join(' ');
}

export function textInputMessageVariants(
	config: {
		error?: boolean;
		color?: TextInputColor;
	} = {}
): string {
	const { error = false, color = TextInputColor.NEUTRAL } = config;

	if (error) {
		return [baseMessageStyles, 'text-destructive'].filter(Boolean).join(' ');
	}

	const colorClasses = {
		[TextInputColor.NEUTRAL]: 'text-muted-foreground',
		[TextInputColor.SUCCESS]: 'text-green-600',
		[TextInputColor.WARNING]: 'text-yellow-600',
		[TextInputColor.INFO]: 'text-blue-600'
	};

	return [baseMessageStyles, colorClasses[color]].filter(Boolean).join(' ');
}

export function textInputLabelVariants(config: TextInputConfig = {}): string {
	const { size = ComponentSize.MD, class: additionalClasses = '' } = config;

	return [baseLabelStyles, sizeStyles[size].label, additionalClasses].filter(Boolean).join(' ');
}
