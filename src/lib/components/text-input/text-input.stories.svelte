<script module lang="ts">
	import { defineMeta } from '@storybook/addon-svelte-csf';
	import { StoryWrapper, AdornmentIcon } from './story-wrapper/index.js';

	import { TextInputType, TextInputColor } from '$lib/components/text-input/text-input.enums.js';
	import { ComponentSize } from '$lib/enums/index.js';

	const { Story } = defineMeta({
		title: 'Components/TextInput',
		component: StoryWrapper,
		tags: ['autodocs'],
		parameters: {
			controls: {
				include: [
					'icon',
					'label',
					'placeholder',
					'type',
					'size',
					'disabled',
					'required',
					'multiline',
					'rows',
					'adornmentPosition',
					'showClearButton',
					'helperText',
					'error',
					'color'
				]
			},
			layout: 'centered'
		},
		argTypes: {
			icon: {
				control: { type: 'select' },
				options: [null, ...Object.values(AdornmentIcon)],
				description: 'Icon to display as adornment'
			},
			type: {
				control: { type: 'select' },
				options: Object.values(TextInputType),
				description: 'The input type'
			},
			size: {
				control: { type: 'select' },
				options: Object.values(ComponentSize),
				description: 'The size of the input'
			},
			adornmentPosition: {
				control: { type: 'select' },
				options: ['left', 'right'],
				description: 'Position of the adornment'
			},
			label: {
				control: { type: 'text' },
				description: 'Label text for the input'
			},
			placeholder: {
				control: { type: 'text' },
				description: 'Placeholder text'
			},
			disabled: {
				control: { type: 'boolean' },
				description: 'Whether the input is disabled'
			},
			required: {
				control: { type: 'boolean' },
				description: 'Whether the input is required'
			},
			multiline: {
				control: { type: 'boolean' },
				description: 'Whether to render as textarea'
			},
			rows: {
				control: { type: 'number' },
				description: 'Number of rows for textarea'
			},
			showClearButton: {
				control: { type: 'boolean' },
				description: 'Whether to show clear button'
			},
			helperText: {
				control: { type: 'text' },
				description: 'Message to display'
			},
			error: {
				control: { type: 'boolean' },
				description: 'If any error occurred'
			},
			color: {
				control: { type: 'select' },
				options: Object.values(TextInputColor),
				description: 'Color variant for helper text (overridden by error state)'
			}
		}
	});
</script>

<Story
	name="Default"
	args={{
		label: 'Username',
		placeholder: 'Enter your username',
		name: 'username',
		icon: AdornmentIcon.USER
	}}
/>

<Story
	name="With Search Icon"
	args={{
		icon: AdornmentIcon.SEARCH,
		label: 'Search',
		placeholder: 'Search for items...',
		name: 'search',
		type: TextInputType.SEARCH,
		showClearButton: false
	}}
/>

<Story
	name="Password with Lock Icon"
	args={{
		icon: AdornmentIcon.LOCK,
		label: 'Password',
		placeholder: 'Enter your password',
		name: 'password',
		type: TextInputType.PASSWORD,
		adornmentPosition: 'left',
		showClearButton: true,
		required: true
	}}
/>

<Story
	name="Textarea"
	args={{
		label: 'Description',
		placeholder: 'Enter a detailed description...',
		name: 'description',
		multiline: true,
		rows: 4
	}}
/>

<Story
	name="With Error Message"
	args={{
		icon: AdornmentIcon.USER,
		label: 'Email',
		placeholder: 'Enter your email',
		name: 'email-error',
		type: TextInputType.EMAIL,
		helperText: 'Please enter a valid email address',
		value: 'invalid-email',
		error: true
	}}
/>

<Story
	name="With Success Message"
	args={{
		icon: AdornmentIcon.USER,
		label: 'Email',
		placeholder: 'Enter your email',
		name: 'email-success',
		type: TextInputType.EMAIL,
		helperText: 'Email address is valid',
		value: '<EMAIL>',
		error: false,
		color: TextInputColor.SUCCESS
	}}
/>

<Story
	name="Disabled"
	args={{
		icon: AdornmentIcon.USER,
		label: 'Username',
		placeholder: 'Enter your username',
		name: 'username-disabled',
		disabled: true,
		value: 'disabled-user'
	}}
/>

<Story
	name="With Warning Message"
	args={{
		icon: AdornmentIcon.USER,
		label: 'Password',
		placeholder: 'Enter your password',
		name: 'password-warning',
		type: TextInputType.PASSWORD,
		helperText: 'Password strength: weak',
		value: '123',
		color: TextInputColor.WARNING
	}}
/>

<Story
	name="With Info Message"
	args={{
		icon: AdornmentIcon.USER,
		label: 'Username',
		placeholder: 'Enter your username',
		name: 'username-info',
		helperText: 'Username must be unique across the platform',
		color: TextInputColor.INFO
	}}
/>
