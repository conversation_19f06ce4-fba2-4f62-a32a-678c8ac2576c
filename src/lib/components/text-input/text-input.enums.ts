export const TextInputType = Object.freeze({
	TEXT: 'text',
	PASSWORD: 'password',
	EMAIL: 'email',
	NUMBER: 'number',
	TEL: 'tel',
	URL: 'url',
	SEARCH: 'search'
} as const);

export type TextInputType = (typeof TextInputType)[keyof typeof TextInputType];

export const TextInputColor = Object.freeze({
	NEUTRAL: 'neutral',
	SUCCESS: 'success',
	WARNING: 'warning',
	INFO: 'info'
} as const);

export type TextInputColor = (typeof TextInputColor)[keyof typeof TextInputColor];
