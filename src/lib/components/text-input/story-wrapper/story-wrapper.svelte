<script lang="ts">
	import type { StoryWrapperProps } from './story-wrapper.types.js';
	import { AdornmentIcon } from './story-wrapper.enums.js';
	import { TextInput } from '../index.js';
	import { User, MagnifyingGlass, Lock } from 'phosphor-svelte';

	let {
		children,
		icon,
		label,
		placeholder,
		disabled,
		helperText,
		value,
		multiline,
		rows,
		adornmentPosition,
		type,
		size,
		name,
		class: className,
		min,
		max,
		required,
		oninput,
		error,
		showClearButton,
		...restProps
	}: StoryWrapperProps = $props();
</script>

{#snippet iconAdornment()}
	{#if icon === AdornmentIcon.SEARCH}
		<MagnifyingGlass size={20} />
	{:else if icon === AdornmentIcon.USER}
		<User size={20} />
	{:else if icon === AdornmentIcon.LOCK}
		<Lock size={20} />
	{/if}
{/snippet}

<div>
	<TextInput
		{label}
		{placeholder}
		{disabled}
		{helperText}
		{value}
		{multiline}
		{rows}
		{adornmentPosition}
		{type}
		{size}
		{name}
		class={className}
		{min}
		{max}
		{required}
		{oninput}
		{error}
		{showClearButton}
		adornment={icon ? iconAdornment : undefined}
		{...restProps}
	/>
</div>
