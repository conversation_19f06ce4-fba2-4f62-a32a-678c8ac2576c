# TextInput Story Wrapper

## Why This Exists

The `StoryWrapper` component exists to solve a specific problem with Storybook and Svelte snippets.

### The Problem

The TextInput component uses Svelte 5 snippets for adornments (icons):

```svelte
<TextInput label="Search" placeholder="Search...">
  {#snippet adornment()}
    <MagnifyingGlass size={20} />
  {/snippet}
</TextInput>
```

However, Storybook's `<Story>` component cannot handle snippet syntax directly. When you try to pass snippets through Storybook's args system, it fails because snippets aren't serializable props.

### The Solution

The `StoryWrapper` component acts as a bridge between Storybook and the TextInput component:

1. **Takes simple props** - Instead of complex snippets, it accepts an `icon` enum value
2. **Converts to snippets** - Internally converts the icon enum to the appropriate snippet
3. **Passes through other props** - All other TextInput props work normally through Storybook's args

### How It Works

```svelte
<!-- In Storybook stories -->
<Story 
  args={{
    icon: AdornmentIcon.SEARCH,
    label: "Search",
    placeholder: "Search for items..."
  }}
/>
```

The wrapper:
1. Receives the `icon` prop from Storybook args
2. Creates the appropriate snippet based on the icon value
3. Passes the snippet to TextInput as the `adornment` prop
4. When `icon` is null/undefined, no adornment is passed (no spacing)

### Benefits

- ✅ **Storybook compatibility** - Works with Storybook's args system
- ✅ **Interactive controls** - Users can change icons via dropdown
- ✅ **No spacing issues** - Conditional rendering prevents empty space
- ✅ **Type safety** - Uses enum for icon selection
- ✅ **Simple API** - Clean props instead of complex snippet syntax

This wrapper is specific to TextInput and lives inside the component folder because it's not a generic solution - it's tailored specifically for TextInput's adornment functionality.
