import type { TextInputProps } from '../text-input.types.js';
import type { AdornmentIcon } from './story-wrapper.enums.js';
import type { Snippet } from 'svelte';

// StoryWrapper props - TextInput props without the adornment snippet, plus icon selection
export type StoryWrapperProps = Omit<TextInputProps, 'adornment'> & {
	icon?: AdornmentIcon;
	children?: Snippet;
};

// Just the TextInput props without adornment for spreading
export type TextInputPropsWithoutAdornment = Omit<TextInputProps, 'adornment'>;
